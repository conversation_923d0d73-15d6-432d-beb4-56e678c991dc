# Media Downloader HD + YouTube Chrome Extension

A powerful Chrome Extension that allows you to download images, videos, and YouTube videos with HD quality options.

## Features

- **🎬 YouTube Video Downloads** - Download YouTube videos directly from URLs in various qualities
- **HD Quality Downloads** - Download images and videos in Full HD (1080p) and 4K quality
- **Quality Selection** - Choose from 720p, 1080p, 1440p (2K), 2160p (4K), or Auto Detect Best
- **Smart URL Processing** - Automatically attempts to find higher quality versions of media
- **Force HD Option** - Tries to replace low-quality URLs with HD versions
- **Quality Filename Tags** - Option to add quality indicators to downloaded filenames
- **Audio Options** - Choose to include or exclude audio for YouTube downloads
- **Media Information Display** - Shows detected media type, estimated quality, and YouTube video info
- Support for common media formats (JPG, PNG, GIF, MP4, AVI, etc.)
- Support for YouTube URLs (youtube.com, youtu.be)
- Clean and intuitive popup interface with HD controls
- URL validation and error handling
- Manifest V3 compliant

## Installation

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right corner
3. Click "Load unpacked" and select the extension folder
4. The Media Downloader icon should appear in your extensions toolbar

## Usage

1. Click the Media Downloader HD + YouTube extension icon in your toolbar
2. Enter the URL of the media you want to download:
   - **YouTube**: `https://www.youtube.com/watch?v=VIDEO_ID` or `https://youtu.be/VIDEO_ID`
   - **Images/Videos**: Direct URLs to media files
3. **Select Quality Options:**
   - **Original Quality**: Download the file as-is
   - **HD (720p)**: Download in 720p quality
   - **Full HD (1080p)**: Download in 1080p quality
   - **2K (1440p)**: Download in 1440p quality (YouTube)
   - **4K (2160p)**: Download in 4K quality (YouTube)
   - **Auto Detect Best**: Automatically select the best available quality
4. **Configure Options:**
   - **Force HD Download**: Tries to modify URLs to find higher quality versions
   - **Add Quality to Filename**: Adds quality tags like "_1080p" or "_4k" to filenames
   - **Include Audio (YouTube)**: Include audio track in YouTube downloads
5. Click the "Download HD" button
6. Choose where to save the file when prompted

## HD Quality Features

### Smart URL Processing
The extension automatically tries to improve URLs for better quality:
- Replaces `/thumb/` with `/original/`
- Changes `_small.jpg` to `_large.jpg`
- Adds HD parameters for supported platforms (like Imgur)

### Media Information
When you enter a URL, the extension shows:
- **Media Type**: Image or Video
- **Estimated Quality**: Based on URL analysis
- **Selected Quality**: Your chosen download quality

### Quality Filename Tags
When enabled, adds quality indicators to filenames:
- `image_1080p.jpg` for Full HD
- `video_4k.mp4` for 4K
- `YouTube_Video_720p.mp4` for YouTube videos

### YouTube Download Features
- **Automatic Video Detection**: Recognizes YouTube URLs and extracts video information
- **Multiple Quality Options**: Support for 720p, 1080p, 1440p, and 4K downloads
- **Audio Control**: Choose to include or exclude audio tracks
- **Smart Filename Generation**: Creates clean filenames from video titles
- **Video ID Display**: Shows the YouTube video ID for reference

### Supported YouTube URL Formats
- `https://www.youtube.com/watch?v=VIDEO_ID`
- `https://youtu.be/VIDEO_ID`
- `https://www.youtube.com/embed/VIDEO_ID`
- `https://www.youtube.com/v/VIDEO_ID`

## Supported File Types

### Images
- JPG/JPEG
- PNG
- GIF
- BMP
- WebP
- SVG
- ICO
- TIFF

### Videos
- MP4
- AVI
- MOV
- WMV
- FLV
- WebM
- MKV
- M4V
- 3GP
- OGV

## Notes

- The extension requires the "downloads" and "activeTab" permissions to function
- Files will be saved to your default download location
- The extension will prompt you to choose a save location for each download
- Make sure the URL points directly to a media file or is a valid YouTube URL
- **YouTube Downloads**: This is a demonstration implementation. For production use, you would need proper YouTube API integration or third-party services
- The extension respects YouTube's terms of service - only download content you have permission to download

## Troubleshooting

- **"Please enter a valid URL"**: Make sure the URL is properly formatted (starts with http:// or https://)
- **"URL should point to an image or video file"**: The URL must contain a recognized media file extension
- **Download fails**: The server might not allow direct downloads or the file might not exist

## Privacy

This extension only downloads files from URLs you provide. It does not collect or transmit any personal data.
