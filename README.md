# Media Downloader HD Chrome Extension

A powerful Chrome Extension that allows you to download images and videos from URLs with HD quality options.

## Features

- **HD Quality Downloads** - Download images and videos in Full HD (1080p) and 4K quality
- **Quality Selection** - Choose from Original, Full HD, 4K, or Auto Detect Best quality
- **Smart URL Processing** - Automatically attempts to find higher quality versions of media
- **Force HD Option** - Tries to replace low-quality URLs with HD versions
- **Quality Filename Tags** - Option to add quality indicators to downloaded filenames
- **Media Information Display** - Shows detected media type and estimated quality
- Support for common media formats (JPG, PNG, GIF, MP4, AVI, etc.)
- Clean and intuitive popup interface with HD controls
- URL validation and error handling
- Manifest V3 compliant

## Installation

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right corner
3. Click "Load unpacked" and select the extension folder
4. The Media Downloader icon should appear in your extensions toolbar

## Usage

1. Click the Media Downloader HD extension icon in your toolbar
2. Enter the URL of the image or video you want to download
3. **Select Quality Options:**
   - **Original Quality**: Download the file as-is
   - **Full HD (1080p)**: Attempt to find/download HD version
   - **4K Ultra HD**: Attempt to find/download 4K version
   - **Auto Detect Best**: Automatically select the best available quality
4. **Configure Options:**
   - **Force HD Download**: Tries to modify URLs to find higher quality versions
   - **Add Quality to Filename**: Adds quality tags like "_hd" or "_4k" to filenames
5. Click the "Download HD" button
6. Choose where to save the file when prompted

## HD Quality Features

### Smart URL Processing
The extension automatically tries to improve URLs for better quality:
- Replaces `/thumb/` with `/original/`
- Changes `_small.jpg` to `_large.jpg`
- Adds HD parameters for supported platforms (like Imgur)

### Media Information
When you enter a URL, the extension shows:
- **Media Type**: Image or Video
- **Estimated Quality**: Based on URL analysis
- **Selected Quality**: Your chosen download quality

### Quality Filename Tags
When enabled, adds quality indicators to filenames:
- `image_hd.jpg` for Full HD
- `video_4k.mp4` for 4K
- `photo_original.png` for original quality

## Supported File Types

### Images
- JPG/JPEG
- PNG
- GIF
- BMP
- WebP
- SVG
- ICO
- TIFF

### Videos
- MP4
- AVI
- MOV
- WMV
- FLV
- WebM
- MKV
- M4V
- 3GP
- OGV

## Notes

- The extension requires the "downloads" permission to function
- Files will be saved to your default download location
- The extension will prompt you to choose a save location for each download
- Make sure the URL points directly to a media file

## Troubleshooting

- **"Please enter a valid URL"**: Make sure the URL is properly formatted (starts with http:// or https://)
- **"URL should point to an image or video file"**: The URL must contain a recognized media file extension
- **Download fails**: The server might not allow direct downloads or the file might not exist

## Privacy

This extension only downloads files from URLs you provide. It does not collect or transmit any personal data.
