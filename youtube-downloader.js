// YouTube Downloader Service
// Alternative approach to handle YouTube downloads without CORS issues

class YouTubeDownloader {
    constructor() {
        this.apiEndpoints = [
            'https://api.cobalt.tools/api/json',
            'https://api.y2mate.com/api/analyze/ajax',
            'https://api.savefrom.net/info'
        ];
    }

    // Extract video ID from various YouTube URL formats
    extractVideoId(url) {
        const patterns = [
            /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)([^&\n?#]+)/
        ];
        
        for (const pattern of patterns) {
            const match = url.match(pattern);
            if (match) {
                return match[1];
            }
        }
        return null;
    }

    // Get video information using alternative methods
    async getVideoInfo(videoId) {
        // Method 1: Try using YouTube thumbnail API (no CORS)
        try {
            const thumbnailUrl = `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`;
            
            // Create basic video info
            const videoInfo = {
                videoId: videoId,
                title: `YouTube Video ${videoId}`,
                thumbnail: thumbnailUrl,
                formats: this.generateFormatOptions(videoId)
            };

            return videoInfo;
        } catch (error) {
            throw new Error('Failed to get video information');
        }
    }

    // Generate download format options
    generateFormatOptions(videoId) {
        return [
            {
                quality: '720p',
                format: 'mp4',
                hasAudio: true,
                downloadUrl: this.generateDownloadUrl(videoId, '720p')
            },
            {
                quality: '1080p',
                format: 'mp4',
                hasAudio: true,
                downloadUrl: this.generateDownloadUrl(videoId, '1080p')
            },
            {
                quality: '1440p',
                format: 'mp4',
                hasAudio: true,
                downloadUrl: this.generateDownloadUrl(videoId, '1440p')
            },
            {
                quality: '2160p',
                format: 'mp4',
                hasAudio: true,
                downloadUrl: this.generateDownloadUrl(videoId, '2160p')
            }
        ];
    }

    // Generate download URL (placeholder for real implementation)
    generateDownloadUrl(videoId, quality) {
        // In a real implementation, this would use a backend service
        // or a third-party API that handles YouTube downloads
        return `https://youtube-dl-service.example.com/download?v=${videoId}&quality=${quality}`;
    }

    // Alternative: Use yt-dlp compatible service
    async downloadWithYtDlp(videoId, quality) {
        try {
            // This would connect to a backend service running yt-dlp
            const response = await fetch('https://your-backend-service.com/api/download', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    url: `https://www.youtube.com/watch?v=${videoId}`,
                    quality: quality,
                    format: 'mp4'
                })
            });

            if (!response.ok) {
                throw new Error('Download service unavailable');
            }

            const result = await response.json();
            return result.downloadUrl;
        } catch (error) {
            throw new Error('Backend service error: ' + error.message);
        }
    }

    // Create a blob URL for download (for demonstration)
    createDemoDownload(videoId, quality) {
        // Create a demo file for testing
        const content = `YouTube Video Download Demo\n\nVideo ID: ${videoId}\nQuality: ${quality}\nTimestamp: ${new Date().toISOString()}\n\nThis is a demonstration file.\nIn a real implementation, this would be the actual video file.`;
        
        const blob = new Blob([content], { type: 'text/plain' });
        return URL.createObjectURL(blob);
    }

    // Main download method
    async initiateDownload(videoId, quality, options = {}) {
        try {
            // For demonstration, create a demo download
            if (options.demo) {
                return this.createDemoDownload(videoId, quality);
            }

            // Try to get real download URL
            try {
                return await this.downloadWithYtDlp(videoId, quality);
            } catch (error) {
                console.log('Backend service not available, using demo mode');
                return this.createDemoDownload(videoId, quality);
            }
        } catch (error) {
            throw new Error('Download failed: ' + error.message);
        }
    }

    // Generate filename for download
    generateFilename(videoInfo, quality, addQuality = false) {
        let filename = videoInfo.title || `YouTube_Video_${videoInfo.videoId}`;
        
        // Clean filename
        filename = filename
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '_')
            .substring(0, 50);

        // Add quality suffix
        if (addQuality) {
            filename += `_${quality}`;
        }

        filename += '.mp4';
        return filename;
    }
}

// Export for use in popup.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = YouTubeDownloader;
} else {
    window.YouTubeDownloader = YouTubeDownloader;
}
