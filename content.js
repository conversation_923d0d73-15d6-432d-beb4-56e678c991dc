// Content Script for YouTube Integration
// This script runs on YouTube pages to extract video information

(function() {
    'use strict';

    // YouTube video info extraction
    function extractYouTubeVideoInfo() {
        try {
            // Get video ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const videoId = urlParams.get('v') || window.location.pathname.split('/').pop();
            
            if (!videoId) {
                return null;
            }

            // Extract video title
            let title = 'YouTube Video';
            const titleElement = document.querySelector('h1.ytd-video-primary-info-renderer yt-formatted-string') ||
                                document.querySelector('h1.title') ||
                                document.querySelector('meta[property="og:title"]');
            
            if (titleElement) {
                title = titleElement.textContent || titleElement.getAttribute('content') || title;
            }

            // Extract video duration
            let duration = 'Unknown';
            const durationElement = document.querySelector('span.ytp-time-duration') ||
                                  document.querySelector('meta[property="video:duration"]');
            
            if (durationElement) {
                duration = durationElement.textContent || durationElement.getAttribute('content') || duration;
            }

            // Extract channel name
            let channel = 'Unknown Channel';
            const channelElement = document.querySelector('#owner-name a') ||
                                 document.querySelector('meta[property="og:video:tag"]');
            
            if (channelElement) {
                channel = channelElement.textContent || channelElement.getAttribute('content') || channel;
            }

            // Extract thumbnail
            let thumbnail = '';
            const thumbnailElement = document.querySelector('meta[property="og:image"]');
            if (thumbnailElement) {
                thumbnail = thumbnailElement.getAttribute('content') || '';
            }

            // Try to extract available qualities from player data
            let availableQualities = ['720p', '1080p'];
            
            // Look for ytInitialPlayerResponse in page scripts
            const scripts = document.querySelectorAll('script');
            for (const script of scripts) {
                if (script.textContent && script.textContent.includes('ytInitialPlayerResponse')) {
                    try {
                        const match = script.textContent.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
                        if (match) {
                            const playerResponse = JSON.parse(match[1]);
                            if (playerResponse.streamingData && playerResponse.streamingData.formats) {
                                const formats = playerResponse.streamingData.formats;
                                availableQualities = formats
                                    .filter(f => f.qualityLabel)
                                    .map(f => f.qualityLabel)
                                    .filter((q, i, arr) => arr.indexOf(q) === i); // Remove duplicates
                            }
                        }
                    } catch (e) {
                        console.log('Could not parse player response:', e);
                    }
                    break;
                }
            }

            return {
                videoId: videoId,
                title: title.trim(),
                channel: channel.trim(),
                duration: duration,
                thumbnail: thumbnail,
                availableQualities: availableQualities,
                url: window.location.href
            };

        } catch (error) {
            console.error('Error extracting YouTube video info:', error);
            return null;
        }
    }

    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === 'getYouTubeVideoInfo') {
            const videoInfo = extractYouTubeVideoInfo();
            sendResponse({
                success: !!videoInfo,
                data: videoInfo,
                error: videoInfo ? null : 'Could not extract video information'
            });
        }
        return true; // Keep message channel open for async response
    });

    // Auto-detect when user is on a video page and send info to popup if it's open
    function notifyVideoDetected() {
        const videoInfo = extractYouTubeVideoInfo();
        if (videoInfo) {
            chrome.runtime.sendMessage({
                action: 'youtubeVideoDetected',
                data: videoInfo
            }).catch(() => {
                // Popup might not be open, ignore error
            });
        }
    }

    // Watch for URL changes (YouTube is a SPA)
    let currentUrl = window.location.href;
    const observer = new MutationObserver(() => {
        if (window.location.href !== currentUrl) {
            currentUrl = window.location.href;
            setTimeout(notifyVideoDetected, 1000); // Wait for page to load
        }
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Initial detection
    if (document.readyState === 'complete') {
        setTimeout(notifyVideoDetected, 1000);
    } else {
        window.addEventListener('load', () => {
            setTimeout(notifyVideoDetected, 1000);
        });
    }

})();
