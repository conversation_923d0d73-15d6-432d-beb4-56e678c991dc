document.addEventListener('DOMContentLoaded', function() {
    const urlInput = document.getElementById('urlInput');
    const downloadBtn = document.getElementById('downloadBtn');
    const statusDiv = document.getElementById('status');
    const qualitySelect = document.getElementById('qualitySelect');
    const forceHDCheckbox = document.getElementById('forceHD');
    const addQualityToNameCheckbox = document.getElementById('addQualityToName');
    const downloadAudioCheckbox = document.getElementById('downloadAudio');
    const mediaInfoDiv = document.getElementById('mediaInfo');

    // Add event listeners
    downloadBtn.addEventListener('click', handleDownload);
    urlInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleDownload();
        }
    });

    // Clear status when user starts typing
    urlInput.addEventListener('input', function() {
        clearStatus();
        hideMediaInfo();
    });

    // Show media info when URL is entered
    urlInput.addEventListener('blur', function() {
        const url = urlInput.value.trim();
        if (url && isValidUrl(url) && (isMediaUrl(url) || isYouTubeUrl(url))) {
            showMediaInfo(url);
        }
    });

    function handleDownload() {
        const url = urlInput.value.trim();
        
        if (!url) {
            showStatus('Please enter a URL', 'error');
            return;
        }

        if (!isValidUrl(url)) {
            showStatus('Please enter a valid URL', 'error');
            return;
        }

        if (!isMediaUrl(url) && !isYouTubeUrl(url)) {
            showStatus('URL should point to an image, video file, or YouTube video', 'error');
            return;
        }

        if (isYouTubeUrl(url)) {
            downloadYouTubeVideo(url);
        } else {
            downloadMediaHD(url);
        }
    }

    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    function isMediaUrl(url) {
        const mediaExtensions = [
            // Image formats
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico', '.tiff', '.tif',
            // Video formats
            '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v', '.3gp', '.ogv'
        ];

        const urlLower = url.toLowerCase();
        return mediaExtensions.some(ext => urlLower.includes(ext));
    }

    function isYouTubeUrl(url) {
        const youtubePatterns = [
            /^https?:\/\/(www\.)?youtube\.com\/watch\?v=[\w-]+/,
            /^https?:\/\/(www\.)?youtu\.be\/[\w-]+/,
            /^https?:\/\/(www\.)?youtube\.com\/embed\/[\w-]+/,
            /^https?:\/\/(www\.)?youtube\.com\/v\/[\w-]+/
        ];

        return youtubePatterns.some(pattern => pattern.test(url));
    }

    function downloadMediaHD(url) {
        showStatus('Preparing HD download...', 'info');
        downloadBtn.disabled = true;

        // Get quality settings
        const selectedQuality = qualitySelect.value;
        const forceHD = forceHDCheckbox.checked;
        const addQualityToName = addQualityToNameCheckbox.checked;

        // Process URL for HD quality
        const processedUrl = processUrlForQuality(url, selectedQuality, forceHD);
        const filename = getFilenameFromUrl(processedUrl, selectedQuality, addQualityToName);

        showStatus('Starting HD download...', 'info');

        chrome.downloads.download({
            url: processedUrl,
            filename: filename,
            saveAs: true
        }, function(downloadId) {
            if (chrome.runtime.lastError) {
                showStatus('Download failed: ' + chrome.runtime.lastError.message, 'error');
                downloadBtn.disabled = false;
            } else {
                showStatus('HD Download started successfully!', 'success');
                downloadBtn.disabled = false;

                // Clear the input after successful download
                setTimeout(() => {
                    urlInput.value = '';
                    clearStatus();
                    hideMediaInfo();
                }, 2000);
            }
        });
    }

    async function downloadYouTubeVideo(url) {
        showStatus('Processing YouTube video...', 'info');
        downloadBtn.disabled = true;

        try {
            // Get quality settings
            const selectedQuality = qualitySelect.value;
            const includeAudio = downloadAudioCheckbox.checked;
            const addQualityToName = addQualityToNameCheckbox.checked;

            // Extract video ID from URL
            const videoId = extractYouTubeVideoId(url);
            if (!videoId) {
                throw new Error('Invalid YouTube URL');
            }

            showStatus('Fetching video information...', 'info');

            // Get video info and download URLs
            const videoInfo = await getYouTubeVideoInfo(videoId);
            const downloadUrl = selectBestQuality(videoInfo.formats, selectedQuality, includeAudio);

            if (!downloadUrl) {
                throw new Error('No suitable video format found');
            }

            const filename = generateYouTubeFilename(videoInfo, selectedQuality, addQualityToName);

            showStatus('Starting YouTube download...', 'info');

            chrome.downloads.download({
                url: downloadUrl,
                filename: filename,
                saveAs: true
            }, function(downloadId) {
                if (chrome.runtime.lastError) {
                    showStatus('Download failed: ' + chrome.runtime.lastError.message, 'error');
                    downloadBtn.disabled = false;
                } else {
                    showStatus('YouTube download started successfully!', 'success');
                    downloadBtn.disabled = false;

                    // Clear the input after successful download
                    setTimeout(() => {
                        urlInput.value = '';
                        clearStatus();
                        hideMediaInfo();
                    }, 2000);
                }
            });

        } catch (error) {
            showStatus('YouTube download failed: ' + error.message, 'error');
            downloadBtn.disabled = false;
        }
    }

    function getFilenameFromUrl(url, quality = 'original', addQuality = false) {
        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;
            let filename = pathname.split('/').pop();

            // If no filename or extension, generate one
            if (!filename || !filename.includes('.')) {
                const timestamp = new Date().getTime();
                filename = `download_${timestamp}`;
            }

            // Add quality to filename if requested
            if (addQuality && quality !== 'original') {
                const parts = filename.split('.');
                if (parts.length > 1) {
                    const extension = parts.pop();
                    const name = parts.join('.');
                    filename = `${name}_${quality}.${extension}`;
                } else {
                    filename = `${filename}_${quality}`;
                }
            }

            return filename;
        } catch (e) {
            const timestamp = new Date().getTime();
            const qualitySuffix = addQuality && quality !== 'original' ? `_${quality}` : '';
            return `download_${timestamp}${qualitySuffix}`;
        }
    }

    function showStatus(message, type) {
        statusDiv.textContent = message;
        statusDiv.className = `status ${type}`;
    }

    function clearStatus() {
        statusDiv.textContent = '';
        statusDiv.className = 'status';
    }

    function processUrlForQuality(url, quality, forceHD) {
        // For most direct media URLs, we return the original URL
        // This function can be extended to handle specific platforms

        if (quality === 'original') {
            return url;
        }

        // Try to detect and modify URLs for better quality
        let processedUrl = url;

        // Handle common image hosting patterns
        if (forceHD) {
            // Replace common low-quality indicators with HD versions
            processedUrl = processedUrl
                .replace(/\/thumb\//, '/original/')
                .replace(/\/small\//, '/large/')
                .replace(/\/medium\//, '/large/')
                .replace(/_small\./, '_large.')
                .replace(/_medium\./, '_large.')
                .replace(/_thumb\./, '_original.');

            // Add HD parameters for some platforms
            if (processedUrl.includes('imgur.com')) {
                processedUrl = processedUrl.replace(/\.(jpg|jpeg|png|gif)$/i, 'h.$1');
            }
        }

        return processedUrl;
    }

    function showMediaInfo(url) {
        const mediaType = getMediaType(url);
        const estimatedQuality = estimateQuality(url);

        let infoHTML = `
            <div class="info-item">
                <span class="info-label">Media Type:</span> ${mediaType}
            </div>
            <div class="info-item">
                <span class="info-label">Estimated Quality:</span> ${estimatedQuality}
            </div>
            <div class="info-item">
                <span class="info-label">Selected Quality:</span> ${qualitySelect.options[qualitySelect.selectedIndex].text}
            </div>
        `;

        // Add YouTube-specific information
        if (isYouTubeUrl(url)) {
            const videoId = extractYouTubeVideoId(url);
            infoHTML += `
                <div class="youtube-detected">
                    <span class="youtube-icon">📺</span> YouTube video detected!
                    <br><small>Video ID: ${videoId}</small>
                </div>
            `;
        }

        mediaInfoDiv.innerHTML = infoHTML;
        mediaInfoDiv.classList.add('show');
    }

    function hideMediaInfo() {
        mediaInfoDiv.classList.remove('show');
    }

    function getMediaType(url) {
        if (isYouTubeUrl(url)) {
            return 'YouTube Video';
        }

        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico', '.tiff', '.tif'];
        const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v', '.3gp', '.ogv'];

        const urlLower = url.toLowerCase();

        if (imageExtensions.some(ext => urlLower.includes(ext))) {
            return 'Image';
        } else if (videoExtensions.some(ext => urlLower.includes(ext))) {
            return 'Video';
        }

        return 'Unknown';
    }

    function estimateQuality(url) {
        const urlLower = url.toLowerCase();

        if (urlLower.includes('4k') || urlLower.includes('2160p')) {
            return '4K Ultra HD';
        } else if (urlLower.includes('1080p') || urlLower.includes('hd') || urlLower.includes('full')) {
            return 'Full HD (1080p)';
        } else if (urlLower.includes('720p')) {
            return 'HD (720p)';
        } else if (urlLower.includes('480p') || urlLower.includes('sd')) {
            return 'Standard Definition';
        } else if (urlLower.includes('thumb') || urlLower.includes('small') || urlLower.includes('preview')) {
            return 'Low Quality/Thumbnail';
        }

        return 'Unknown';
    }

    // YouTube helper functions
    function extractYouTubeVideoId(url) {
        const patterns = [
            /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)([^&\n?#]+)/
        ];

        for (const pattern of patterns) {
            const match = url.match(pattern);
            if (match) {
                return match[1];
            }
        }
        return null;
    }

    async function getYouTubeVideoInfo(videoId) {
        // This is a simplified approach - in a real implementation, you would need
        // to use YouTube's API or a third-party service to get video information
        // For demonstration purposes, we'll create a mock response

        try {
            // Attempt to fetch video page and extract basic info
            const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`);
            const html = await response.text();

            // Extract title from page
            const titleMatch = html.match(/<title>([^<]+)<\/title>/);
            const title = titleMatch ? titleMatch[1].replace(' - YouTube', '') : `YouTube_Video_${videoId}`;

            // For this demo, we'll return a simplified structure
            // In a real implementation, you'd need proper YouTube API integration
            return {
                title: title,
                videoId: videoId,
                formats: [
                    { quality: '720p', url: `https://example.com/video_${videoId}_720p.mp4`, hasAudio: true },
                    { quality: '1080p', url: `https://example.com/video_${videoId}_1080p.mp4`, hasAudio: true },
                    { quality: '1440p', url: `https://example.com/video_${videoId}_1440p.mp4`, hasAudio: true },
                    { quality: '2160p', url: `https://example.com/video_${videoId}_2160p.mp4`, hasAudio: true }
                ]
            };
        } catch (error) {
            throw new Error('Failed to fetch video information');
        }
    }

    function selectBestQuality(formats, requestedQuality, includeAudio) {
        if (!formats || formats.length === 0) {
            return null;
        }

        // Filter formats based on audio requirement
        let availableFormats = includeAudio ?
            formats.filter(f => f.hasAudio) :
            formats;

        if (availableFormats.length === 0) {
            availableFormats = formats; // Fallback to any format
        }

        // Find exact quality match
        let selectedFormat = availableFormats.find(f => f.quality === requestedQuality);

        // If no exact match, find the best available quality
        if (!selectedFormat) {
            const qualityOrder = ['2160p', '1440p', '1080p', '720p', '480p', '360p'];
            for (const quality of qualityOrder) {
                selectedFormat = availableFormats.find(f => f.quality === quality);
                if (selectedFormat) break;
            }
        }

        return selectedFormat ? selectedFormat.url : null;
    }

    function generateYouTubeFilename(videoInfo, quality, addQuality) {
        let filename = videoInfo.title
            .replace(/[^\w\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '_')     // Replace spaces with underscores
            .substring(0, 50);        // Limit length

        if (addQuality && quality !== 'original') {
            filename += `_${quality}`;
        }

        filename += '.mp4';
        return filename;
    }
});
