document.addEventListener('DOMContentLoaded', function() {
    const urlInput = document.getElementById('urlInput');
    const downloadBtn = document.getElementById('downloadBtn');
    const statusDiv = document.getElementById('status');
    const qualitySelect = document.getElementById('qualitySelect');
    const forceHDCheckbox = document.getElementById('forceHD');
    const addQualityToNameCheckbox = document.getElementById('addQualityToName');
    const mediaInfoDiv = document.getElementById('mediaInfo');

    // Add event listeners
    downloadBtn.addEventListener('click', handleDownload);
    urlInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleDownload();
        }
    });

    // Clear status when user starts typing
    urlInput.addEventListener('input', function() {
        clearStatus();
        hideMediaInfo();
    });

    // Show media info when URL is entered
    urlInput.addEventListener('blur', function() {
        const url = urlInput.value.trim();
        if (url && isValidUrl(url) && isMediaUrl(url)) {
            showMediaInfo(url);
        }
    });

    function handleDownload() {
        const url = urlInput.value.trim();
        
        if (!url) {
            showStatus('Please enter a URL', 'error');
            return;
        }

        if (!isValidUrl(url)) {
            showStatus('Please enter a valid URL', 'error');
            return;
        }

        if (!isMediaUrl(url)) {
            showStatus('URL should point to an image or video file', 'error');
            return;
        }

        downloadMediaHD(url);
    }

    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    function isMediaUrl(url) {
        const mediaExtensions = [
            // Image formats
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico', '.tiff', '.tif',
            // Video formats
            '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v', '.3gp', '.ogv'
        ];
        
        const urlLower = url.toLowerCase();
        return mediaExtensions.some(ext => urlLower.includes(ext));
    }

    function downloadMediaHD(url) {
        showStatus('Preparing HD download...', 'info');
        downloadBtn.disabled = true;

        // Get quality settings
        const selectedQuality = qualitySelect.value;
        const forceHD = forceHDCheckbox.checked;
        const addQualityToName = addQualityToNameCheckbox.checked;

        // Process URL for HD quality
        const processedUrl = processUrlForQuality(url, selectedQuality, forceHD);
        const filename = getFilenameFromUrl(processedUrl, selectedQuality, addQualityToName);

        showStatus('Starting HD download...', 'info');

        chrome.downloads.download({
            url: processedUrl,
            filename: filename,
            saveAs: true
        }, function(downloadId) {
            if (chrome.runtime.lastError) {
                showStatus('Download failed: ' + chrome.runtime.lastError.message, 'error');
                downloadBtn.disabled = false;
            } else {
                showStatus('HD Download started successfully!', 'success');
                downloadBtn.disabled = false;

                // Clear the input after successful download
                setTimeout(() => {
                    urlInput.value = '';
                    clearStatus();
                    hideMediaInfo();
                }, 2000);
            }
        });
    }

    function getFilenameFromUrl(url, quality = 'original', addQuality = false) {
        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;
            let filename = pathname.split('/').pop();

            // If no filename or extension, generate one
            if (!filename || !filename.includes('.')) {
                const timestamp = new Date().getTime();
                filename = `download_${timestamp}`;
            }

            // Add quality to filename if requested
            if (addQuality && quality !== 'original') {
                const parts = filename.split('.');
                if (parts.length > 1) {
                    const extension = parts.pop();
                    const name = parts.join('.');
                    filename = `${name}_${quality}.${extension}`;
                } else {
                    filename = `${filename}_${quality}`;
                }
            }

            return filename;
        } catch (e) {
            const timestamp = new Date().getTime();
            const qualitySuffix = addQuality && quality !== 'original' ? `_${quality}` : '';
            return `download_${timestamp}${qualitySuffix}`;
        }
    }

    function showStatus(message, type) {
        statusDiv.textContent = message;
        statusDiv.className = `status ${type}`;
    }

    function clearStatus() {
        statusDiv.textContent = '';
        statusDiv.className = 'status';
    }

    function processUrlForQuality(url, quality, forceHD) {
        // For most direct media URLs, we return the original URL
        // This function can be extended to handle specific platforms

        if (quality === 'original') {
            return url;
        }

        // Try to detect and modify URLs for better quality
        let processedUrl = url;

        // Handle common image hosting patterns
        if (forceHD) {
            // Replace common low-quality indicators with HD versions
            processedUrl = processedUrl
                .replace(/\/thumb\//, '/original/')
                .replace(/\/small\//, '/large/')
                .replace(/\/medium\//, '/large/')
                .replace(/_small\./, '_large.')
                .replace(/_medium\./, '_large.')
                .replace(/_thumb\./, '_original.');

            // Add HD parameters for some platforms
            if (processedUrl.includes('imgur.com')) {
                processedUrl = processedUrl.replace(/\.(jpg|jpeg|png|gif)$/i, 'h.$1');
            }
        }

        return processedUrl;
    }

    function showMediaInfo(url) {
        const mediaType = getMediaType(url);
        const estimatedQuality = estimateQuality(url);

        mediaInfoDiv.innerHTML = `
            <div class="info-item">
                <span class="info-label">Media Type:</span> ${mediaType}
            </div>
            <div class="info-item">
                <span class="info-label">Estimated Quality:</span> ${estimatedQuality}
            </div>
            <div class="info-item">
                <span class="info-label">Selected Quality:</span> ${qualitySelect.options[qualitySelect.selectedIndex].text}
            </div>
        `;
        mediaInfoDiv.classList.add('show');
    }

    function hideMediaInfo() {
        mediaInfoDiv.classList.remove('show');
    }

    function getMediaType(url) {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico', '.tiff', '.tif'];
        const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.mkv', '.m4v', '.3gp', '.ogv'];

        const urlLower = url.toLowerCase();

        if (imageExtensions.some(ext => urlLower.includes(ext))) {
            return 'Image';
        } else if (videoExtensions.some(ext => urlLower.includes(ext))) {
            return 'Video';
        }

        return 'Unknown';
    }

    function estimateQuality(url) {
        const urlLower = url.toLowerCase();

        if (urlLower.includes('4k') || urlLower.includes('2160p')) {
            return '4K Ultra HD';
        } else if (urlLower.includes('1080p') || urlLower.includes('hd') || urlLower.includes('full')) {
            return 'Full HD (1080p)';
        } else if (urlLower.includes('720p')) {
            return 'HD (720p)';
        } else if (urlLower.includes('480p') || urlLower.includes('sd')) {
            return 'Standard Definition';
        } else if (urlLower.includes('thumb') || urlLower.includes('small') || urlLower.includes('preview')) {
            return 'Low Quality/Thumbnail';
        }

        return 'Unknown';
    }
});
