# YouTube Integration Guide

## Current Implementation Status

The current implementation includes a **demonstration version** of YouTube download functionality. For production use, you'll need to implement proper YouTube video extraction.

## ⚠️ Important Notes

1. **Terms of Service**: Always respect YouTube's Terms of Service
2. **Legal Compliance**: Only download content you have permission to download
3. **API Limitations**: YouTube doesn't provide official download APIs for third-party apps

## Production Implementation Options

### Option 1: YouTube Data API v3 (Metadata Only)
```javascript
// Get video metadata (title, description, etc.)
const API_KEY = 'your_youtube_api_key';
const videoId = 'VIDEO_ID';
const url = `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&key=${API_KEY}&part=snippet`;
```

**Limitations**: Only provides metadata, not download URLs

### Option 2: Third-Party Services
Popular services that provide YouTube download APIs:
- **yt-dlp**: Python library with extensive format support
- **youtube-dl**: Original Python library
- **Invidious API**: Alternative YouTube frontend with API

### Option 3: Browser-Based Extraction
```javascript
// Extract video information from YouTube page
async function extractVideoInfo(videoId) {
    try {
        const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`);
        const html = await response.text();
        
        // Parse ytInitialPlayerResponse from page
        const playerResponseMatch = html.match(/ytInitialPlayerResponse\s*=\s*({.+?});/);
        if (playerResponseMatch) {
            const playerResponse = JSON.parse(playerResponseMatch[1]);
            return parseVideoFormats(playerResponse);
        }
    } catch (error) {
        console.error('Failed to extract video info:', error);
    }
}
```

## Current Demo Implementation

The current code includes:
- ✅ YouTube URL detection and validation
- ✅ Video ID extraction
- ✅ Basic video information fetching
- ✅ Quality selection interface
- ✅ Audio options
- ⚠️ Mock download URLs (for demonstration)

## To Make It Production-Ready

### 1. Replace Mock URLs
In `popup.js`, update the `getYouTubeVideoInfo` function:

```javascript
async function getYouTubeVideoInfo(videoId) {
    // Replace this with real implementation
    // Options: yt-dlp API, Invidious API, or custom extraction
    
    const response = await fetch(`YOUR_API_ENDPOINT/video/${videoId}`);
    const data = await response.json();
    
    return {
        title: data.title,
        videoId: videoId,
        formats: data.formats.map(format => ({
            quality: format.quality,
            url: format.url,
            hasAudio: format.hasAudio
        }))
    };
}
```

### 2. Add Error Handling
```javascript
try {
    const videoInfo = await getYouTubeVideoInfo(videoId);
    // Process video info
} catch (error) {
    if (error.message.includes('private')) {
        showStatus('Video is private or unavailable', 'error');
    } else if (error.message.includes('age-restricted')) {
        showStatus('Age-restricted content cannot be downloaded', 'error');
    } else {
        showStatus('Failed to process YouTube video', 'error');
    }
}
```

### 3. Add Rate Limiting
```javascript
const rateLimiter = {
    requests: 0,
    resetTime: Date.now() + 60000, // 1 minute
    
    canMakeRequest() {
        if (Date.now() > this.resetTime) {
            this.requests = 0;
            this.resetTime = Date.now() + 60000;
        }
        return this.requests < 10; // Max 10 requests per minute
    },
    
    recordRequest() {
        this.requests++;
    }
};
```

## Recommended Architecture

### Backend Service Approach
1. Create a backend service that handles YouTube extraction
2. Use yt-dlp or similar tools on the server
3. Chrome extension communicates with your backend
4. Backend returns processed download URLs

### Benefits:
- ✅ More reliable extraction
- ✅ Better error handling
- ✅ Rate limiting control
- ✅ Legal compliance easier to manage

## Legal Considerations

1. **Fair Use**: Ensure downloads comply with fair use policies
2. **Copyright**: Respect content creators' rights
3. **Terms of Service**: Follow YouTube's ToS
4. **User Education**: Inform users about legal responsibilities

## Testing

Test with various YouTube URLs:
- Standard videos: `https://www.youtube.com/watch?v=dQw4w9WgXcQ`
- Short URLs: `https://youtu.be/dQw4w9WgXcQ`
- Embedded URLs: `https://www.youtube.com/embed/dQw4w9WgXcQ`
- Private videos (should fail gracefully)
- Age-restricted content (should handle appropriately)

## Next Steps

1. Choose your preferred implementation method
2. Set up backend service (if using backend approach)
3. Replace mock functions with real implementations
4. Add comprehensive error handling
5. Test thoroughly with various video types
6. Ensure legal compliance
