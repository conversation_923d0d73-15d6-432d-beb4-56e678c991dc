<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Generator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-preview {
            display: flex;
            justify-content: center;
            margin: 30px 0;
        }
        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        label {
            font-weight: 600;
            color: #555;
        }
        input, select {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        input:focus, select:focus {
            outline: none;
            border-color: #4CAF50;
        }
        .download-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(76, 175, 80, 0.3);
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
            border-left: 4px solid #2196F3;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1976D2;
        }
        .size-options {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        .size-btn {
            padding: 8px 16px;
            border: 2px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .size-btn.active {
            border-color: #4CAF50;
            background: #4CAF50;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Chrome Extension Icon Generator</h1>
        
        <div class="icon-preview">
            <canvas id="iconCanvas" width="128" height="128"></canvas>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label for="bgColor">Background Color:</label>
                <input type="color" id="bgColor" value="#4CAF50">
            </div>
            
            <div class="control-group">
                <label for="iconColor">Icon Color:</label>
                <input type="color" id="iconColor" value="#ffffff">
            </div>
            
            <div class="control-group">
                <label for="iconType">Icon Type:</label>
                <select id="iconType">
                    <option value="download">Download Arrow</option>
                    <option value="folder">Folder</option>
                    <option value="media">Media/Play</option>
                    <option value="cloud">Cloud Download</option>
                </select>
            </div>
            
            <div class="control-group">
                <label>Export Size:</label>
                <div class="size-options">
                    <button class="size-btn active" data-size="128">128px</button>
                    <button class="size-btn" data-size="256">256px</button>
                    <button class="size-btn" data-size="512">512px</button>
                </div>
            </div>
        </div>
        
        <button class="download-btn" id="downloadBtn">Download icon.png</button>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Customize your icon using the controls above</li>
                <li>Click "Download icon.png" to save the icon file</li>
                <li>Place the downloaded "icon.png" file in your Chrome Extension folder</li>
                <li>The file should be in the same directory as manifest.json</li>
                <li>Reload your extension in Chrome (chrome://extensions/)</li>
            </ol>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('iconCanvas');
        const ctx = canvas.getContext('2d');
        const bgColorInput = document.getElementById('bgColor');
        const iconColorInput = document.getElementById('iconColor');
        const iconTypeSelect = document.getElementById('iconType');
        const downloadBtn = document.getElementById('downloadBtn');
        const sizeButtons = document.querySelectorAll('.size-btn');
        
        let currentSize = 128;
        
        // Event listeners
        bgColorInput.addEventListener('input', drawIcon);
        iconColorInput.addEventListener('input', drawIcon);
        iconTypeSelect.addEventListener('change', drawIcon);
        downloadBtn.addEventListener('click', downloadIcon);
        
        sizeButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                sizeButtons.forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                currentSize = parseInt(e.target.dataset.size);
            });
        });
        
        function drawIcon() {
            const bgColor = bgColorInput.value;
            const iconColor = iconColorInput.value;
            const iconType = iconTypeSelect.value;
            
            // Clear canvas
            ctx.clearRect(0, 0, 128, 128);
            
            // Draw background with rounded corners
            ctx.fillStyle = bgColor;
            roundRect(ctx, 0, 0, 128, 128, 20);
            ctx.fill();
            
            // Draw icon
            ctx.fillStyle = iconColor;
            ctx.strokeStyle = iconColor;
            ctx.lineWidth = 6;
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            
            switch(iconType) {
                case 'download':
                    drawDownloadIcon();
                    break;
                case 'folder':
                    drawFolderIcon();
                    break;
                case 'media':
                    drawMediaIcon();
                    break;
                case 'cloud':
                    drawCloudIcon();
                    break;
            }
        }
        
        function drawDownloadIcon() {
            // Vertical line
            ctx.beginPath();
            ctx.moveTo(64, 25);
            ctx.lineTo(64, 80);
            ctx.stroke();
            
            // Arrow head
            ctx.beginPath();
            ctx.moveTo(45, 65);
            ctx.lineTo(64, 85);
            ctx.lineTo(83, 65);
            ctx.stroke();
            
            // Bottom line
            ctx.beginPath();
            ctx.moveTo(35, 100);
            ctx.lineTo(93, 100);
            ctx.stroke();
        }
        
        function drawFolderIcon() {
            ctx.beginPath();
            ctx.moveTo(25, 45);
            ctx.lineTo(50, 45);
            ctx.lineTo(60, 35);
            ctx.lineTo(103, 35);
            ctx.lineTo(103, 95);
            ctx.lineTo(25, 95);
            ctx.closePath();
            ctx.fill();
        }
        
        function drawMediaIcon() {
            // Play triangle
            ctx.beginPath();
            ctx.moveTo(45, 35);
            ctx.lineTo(45, 93);
            ctx.lineTo(88, 64);
            ctx.closePath();
            ctx.fill();
        }
        
        function drawCloudIcon() {
            // Cloud shape
            ctx.beginPath();
            ctx.arc(50, 55, 15, Math.PI, 0);
            ctx.arc(70, 50, 20, Math.PI, 0);
            ctx.arc(90, 55, 15, Math.PI, 0);
            ctx.lineTo(95, 70);
            ctx.lineTo(35, 70);
            ctx.closePath();
            ctx.fill();
            
            // Download arrow
            ctx.beginPath();
            ctx.moveTo(64, 75);
            ctx.lineTo(64, 95);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(57, 88);
            ctx.lineTo(64, 95);
            ctx.lineTo(71, 88);
            ctx.stroke();
        }
        
        function roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }
        
        function downloadIcon() {
            // Create a temporary canvas with the desired size
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            tempCanvas.width = currentSize;
            tempCanvas.height = currentSize;
            
            // Scale and draw the icon
            tempCtx.scale(currentSize / 128, currentSize / 128);
            tempCtx.drawImage(canvas, 0, 0);
            
            // Download
            tempCanvas.toBlob((blob) => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'icon.png';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 'image/png');
        }
        
        // Initial draw
        drawIcon();
    </script>
</body>
</html>