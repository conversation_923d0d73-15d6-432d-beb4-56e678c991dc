<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Downloader</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <h2>Media Downloader HD</h2>
        <div class="input-group">
            <label for="urlInput">Enter Image/Video URL:</label>
            <input type="url" id="urlInput" placeholder="https://example.com/image.jpg" />
        </div>

        <div class="quality-section">
            <label for="qualitySelect">Quality Options:</label>
            <select id="qualitySelect" class="quality-select">
                <option value="original">Original Quality</option>
                <option value="hd" selected>Full HD (1080p)</option>
                <option value="4k">4K Ultra HD</option>
                <option value="auto">Auto Detect Best</option>
            </select>
        </div>

        <div class="options-section">
            <label class="checkbox-container">
                <input type="checkbox" id="forceHD" checked>
                <span class="checkmark"></span>
                Force HD Download
            </label>
            <label class="checkbox-container">
                <input type="checkbox" id="addQualityToName">
                <span class="checkmark"></span>
                Add Quality to Filename
            </label>
        </div>

        <button id="downloadBtn" class="download-btn">Download HD</button>
        <div id="status" class="status"></div>
        <div id="mediaInfo" class="media-info"></div>
    </div>
    <script src="popup.js"></script>
</body>
</html>
