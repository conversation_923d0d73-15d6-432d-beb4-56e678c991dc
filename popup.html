<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HD Media Downloader</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header Section -->
        <div class="header">
            <div class="logo-section">
                <div class="logo-icon">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                        <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linejoin="round"/>
                    </svg>
                </div>
                <div class="logo-text">
                    <h1>HD Downloader</h1>
                    <span class="version">v2.0</span>
                </div>
            </div>
            <div class="status-indicator">
                <div class="pulse-dot"></div>
                <span>Ready</span>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- URL Input Section -->
            <div class="input-section">
                <div class="input-header">
                    <h3>📎 Media URL</h3>
                    <div class="supported-platforms">
                        <span class="platform youtube">📺</span>
                        <span class="platform image">🖼️</span>
                        <span class="platform video">🎬</span>
                    </div>
                </div>

                <div class="input-wrapper">
                    <div class="input-icon">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10 13C10.4295 13.5741 10.9774 14.0491 11.6066 14.3929C12.2357 14.7367 12.9315 14.9411 13.6467 14.9923C14.3618 15.0435 15.0796 14.9403 15.7513 14.6897C16.4231 14.4392 17.0331 14.047 17.54 13.54L20.54 10.54C21.4508 9.59695 21.9548 8.33394 21.9434 7.02296C21.932 5.71198 21.4061 4.45791 20.4791 3.53087C19.5521 2.60383 18.298 2.07799 16.987 2.0666C15.676 2.0552 14.413 2.55918 13.47 3.47L11.75 5.18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M14 11C13.5705 10.4259 13.0226 9.95085 12.3934 9.60706C11.7643 9.26327 11.0685 9.05885 10.3533 9.00763C9.63819 8.95641 8.92037 9.05963 8.24864 9.31018C7.5769 9.56073 6.9669 9.95295 6.46 10.46L3.46 13.46C2.54918 14.403 2.04520 15.6661 2.05660 16.977C2.06799 18.288 2.59383 19.5421 3.52087 20.4691C4.44791 21.3962 5.70198 21.922 7.01296 21.9334C8.32394 21.9448 9.58695 21.4408 10.53 20.53L12.24 18.82" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <input type="url" id="urlInput" placeholder="Paste your YouTube, image, or video URL here..." />
                    <div class="input-actions">
                        <button class="paste-btn" id="pasteBtn">📋</button>
                        <button class="clear-btn" id="clearBtn">✕</button>
                    </div>
                </div>

                <div class="url-examples">
                    <div class="example-item">
                        <span class="example-icon">📺</span>
                        <span>youtube.com/watch?v=...</span>
                    </div>
                    <div class="example-item">
                        <span class="example-icon">🖼️</span>
                        <span>example.com/image.jpg</span>
                    </div>
                </div>
            </div>

            <!-- Quality Selection -->
            <div class="quality-section">
                <div class="section-header">
                    <h3>🎯 Quality Settings</h3>
                    <div class="quality-badge" id="qualityBadge">HD</div>
                </div>

                <div class="quality-grid">
                    <div class="quality-option" data-quality="720p">
                        <div class="quality-icon">📺</div>
                        <div class="quality-info">
                            <span class="quality-name">HD</span>
                            <span class="quality-res">720p</span>
                        </div>
                        <div class="quality-radio">
                            <input type="radio" name="quality" value="720p" id="q720p">
                        </div>
                    </div>

                    <div class="quality-option active" data-quality="1080p">
                        <div class="quality-icon">🔥</div>
                        <div class="quality-info">
                            <span class="quality-name">Full HD</span>
                            <span class="quality-res">1080p</span>
                        </div>
                        <div class="quality-radio">
                            <input type="radio" name="quality" value="1080p" id="q1080p" checked>
                        </div>
                    </div>

                    <div class="quality-option" data-quality="1440p">
                        <div class="quality-icon">💎</div>
                        <div class="quality-info">
                            <span class="quality-name">2K</span>
                            <span class="quality-res">1440p</span>
                        </div>
                        <div class="quality-radio">
                            <input type="radio" name="quality" value="1440p" id="q1440p">
                        </div>
                    </div>

                    <div class="quality-option" data-quality="2160p">
                        <div class="quality-icon">👑</div>
                        <div class="quality-info">
                            <span class="quality-name">4K Ultra</span>
                            <span class="quality-res">2160p</span>
                        </div>
                        <div class="quality-radio">
                            <input type="radio" name="quality" value="2160p" id="q2160p">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Options -->
            <div class="options-section">
                <div class="section-header">
                    <h3>⚙️ Advanced Options</h3>
                </div>

                <div class="options-grid">
                    <div class="option-card">
                        <div class="option-icon">🚀</div>
                        <div class="option-content">
                            <label class="option-label">Force HD Download</label>
                            <p class="option-desc">Automatically find higher quality versions</p>
                        </div>
                        <div class="toggle-switch">
                            <input type="checkbox" id="forceHD" checked>
                            <span class="slider"></span>
                        </div>
                    </div>

                    <div class="option-card">
                        <div class="option-icon">🏷️</div>
                        <div class="option-content">
                            <label class="option-label">Quality Tags</label>
                            <p class="option-desc">Add quality info to filename</p>
                        </div>
                        <div class="toggle-switch">
                            <input type="checkbox" id="addQualityToName">
                            <span class="slider"></span>
                        </div>
                    </div>

                    <div class="option-card youtube-only">
                        <div class="option-icon">🔊</div>
                        <div class="option-content">
                            <label class="option-label">Include Audio</label>
                            <p class="option-desc">Download with audio track (YouTube)</p>
                        </div>
                        <div class="toggle-switch">
                            <input type="checkbox" id="downloadAudio" checked>
                            <span class="slider"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Download Button -->
            <div class="download-section">
                <button id="downloadBtn" class="download-btn">
                    <div class="btn-icon">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <span class="btn-text">Download HD</span>
                    <div class="btn-progress"></div>
                </button>
            </div>

            <!-- Status & Info -->
            <div id="status" class="status-section"></div>
            <div id="mediaInfo" class="media-info-section"></div>
        </div>
    </div>
    <script src="popup.js"></script>
</body>
</html>
