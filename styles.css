body {
    width: 380px;
    min-height: 300px;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
}

.container {
    padding: 20px;
}

h2 {
    margin: 0 0 20px 0;
    color: #333;
    text-align: center;
    font-size: 18px;
}

.input-group {
    margin-bottom: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    color: #555;
    font-size: 14px;
    font-weight: 500;
}

#urlInput {
    width: 100%;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    box-sizing: border-box;
    transition: border-color 0.3s ease;
}

#urlInput:focus {
    outline: none;
    border-color: #4CAF50;
}

.download-btn {
    width: 100%;
    padding: 12px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.download-btn:hover {
    background-color: #45a049;
}

.download-btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.status {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    font-size: 14px;
    text-align: center;
    min-height: 20px;
}

.status.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status.info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Quality Section Styles */
.quality-section {
    margin-bottom: 15px;
}

.quality-select {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.quality-select:focus {
    outline: none;
    border-color: #4CAF50;
}

/* Options Section Styles */
.options-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    cursor: pointer;
    font-size: 14px;
    color: #555;
}

.checkbox-container:last-child {
    margin-bottom: 0;
}

.checkbox-container input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
    cursor: pointer;
}

.checkmark {
    margin-left: 5px;
}

/* Media Info Styles */
.media-info {
    margin-top: 15px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    font-size: 13px;
    color: #6c757d;
    display: none;
}

.media-info.show {
    display: block;
}

.media-info .info-item {
    margin-bottom: 5px;
}

.media-info .info-item:last-child {
    margin-bottom: 0;
}

.media-info .info-label {
    font-weight: 600;
    color: #495057;
}
