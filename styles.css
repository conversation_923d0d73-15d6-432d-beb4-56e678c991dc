/* Modern CSS Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 420px;
    min-height: 600px;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow-x: hidden;
}

.app-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    margin: 8px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-icon svg {
    width: 20px;
    height: 20px;
    color: white;
}

.logo-text h1 {
    font-size: 18px;
    font-weight: 700;
    margin: 0;
}

.version {
    font-size: 11px;
    opacity: 0.8;
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 4px;
    margin-left: 8px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    opacity: 0.9;
}

.pulse-dot {
    width: 8px;
    height: 8px;
    background: #4ade80;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Main Content */
.main-content {
    padding: 24px;
}

/* Input Section */
.input-section {
    margin-bottom: 24px;
}

.input-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.input-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.supported-platforms {
    display: flex;
    gap: 8px;
}

.platform {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 6px;
    font-size: 12px;
    transition: all 0.2s ease;
}

.platform:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(1.1);
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-icon {
    padding: 0 16px;
    color: #9ca3af;
}

.input-icon svg {
    width: 20px;
    height: 20px;
}

#urlInput {
    flex: 1;
    padding: 16px 0;
    border: none;
    outline: none;
    font-size: 14px;
    color: #1f2937;
    background: transparent;
}

#urlInput::placeholder {
    color: #9ca3af;
}

.input-actions {
    display: flex;
    gap: 4px;
    padding: 8px;
}

.paste-btn, .clear-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
}

.paste-btn:hover, .clear-btn:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(1.05);
}

.url-examples {
    display: flex;
    gap: 12px;
    margin-top: 12px;
}

.example-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 6px;
    font-size: 12px;
    color: #6b7280;
}

.example-icon {
    font-size: 10px;
}

/* Quality Section */
.quality-section {
    margin-bottom: 24px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.section-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.quality-badge {
    padding: 4px 8px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 600;
}

.quality-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.quality-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.quality-option:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.quality-option.active {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.quality-icon {
    font-size: 20px;
    width: 32px;
    text-align: center;
}

.quality-info {
    flex: 1;
}

.quality-name {
    display: block;
    font-weight: 600;
    color: #1f2937;
    font-size: 14px;
}

.quality-res {
    display: block;
    color: #6b7280;
    font-size: 12px;
}

.quality-radio {
    position: relative;
}

.quality-radio input[type="radio"] {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quality-radio input[type="radio"]:checked {
    border-color: #667eea;
    background: #667eea;
}

.quality-radio input[type="radio"]:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
}

/* Options Section */
.options-section {
    margin-bottom: 24px;
}

.options-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.option-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.option-card:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.option-card.youtube-only {
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.option-card.youtube-only.active {
    opacity: 1;
}

.option-icon {
    font-size: 20px;
    width: 32px;
    text-align: center;
}

.option-content {
    flex: 1;
}

.option-label {
    display: block;
    font-weight: 600;
    color: #1f2937;
    font-size: 14px;
    margin-bottom: 2px;
}

.option-desc {
    color: #6b7280;
    font-size: 12px;
    margin: 0;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    width: 44px;
    height: 24px;
}

.toggle-switch input[type="checkbox"] {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #d1d5db;
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* Download Button */
.download-section {
    margin-bottom: 24px;
}

.download-btn {
    width: 100%;
    height: 56px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 16px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.download-btn:active {
    transform: translateY(0);
}

.download-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-icon svg {
    width: 20px;
    height: 20px;
}

.btn-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 0 0 16px 16px;
    width: 0%;
    transition: width 0.3s ease;
}

/* Status Section */
.status-section {
    margin-bottom: 16px;
}

.status-section:empty {
    display: none;
}

.status-section {
    padding: 12px 16px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.status-section.success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.status-section.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.status-section.info {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.status-section::before {
    content: '';
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    animation: pulse 2s infinite;
}

/* Media Info Section */
.media-info-section {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 16px;
    display: none;
    animation: fadeIn 0.3s ease;
}

.media-info-section.show {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.media-info-section .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f3f4f6;
}

.media-info-section .info-item:last-child {
    border-bottom: none;
}

.media-info-section .info-label {
    font-weight: 600;
    color: #374151;
    font-size: 13px;
}

.media-info-section .info-value {
    color: #6b7280;
    font-size: 13px;
}

/* YouTube Detection */
.youtube-detected {
    background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 12px;
    margin-top: 12px;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    animation: slideIn 0.3s ease;
}

.youtube-detected .youtube-icon {
    font-size: 16px;
}

/* Responsive Design */
@media (max-width: 480px) {
    body {
        width: 100vw;
        min-height: 100vh;
    }

    .app-container {
        margin: 0;
        border-radius: 0;
        min-height: 100vh;
    }

    .quality-grid {
        grid-template-columns: 1fr;
    }
}

/* Loading Animation */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}
